#!/usr/bin/env python3
"""
双GUI界面测试脚本
用于验证双GUI功能是否正常工作
"""

import sys
import time
import yaml
from multiprocessing import Process, Queue as MpQueue, Manager

def create_test_config():
    """创建测试配置"""
    return {
        'displayer': {
            'enabled': True,
            'num_windows': 2,
            'window': {
                'title': '测试GUI窗口',
                'default_width_ratio': 0.4,
                'default_height_ratio': 0.6,
                'background_color': '#DDDDDD'
            },
            'layout': {
                'left_panel_ratio': 0.6,
                'queue_panel_height_ratio': 0.7
            },
            'fonts': {
                'global_colors': {
                    'player_name': '#eb24c1'
                },
                'player_font': {
                    'family': 'Segoe UI Emoji Regular',
                    'weight': 'normal'
                },
                'data_font': {
                    'family': 'Microsoft YaHei UI',
                    'weight': 'normal'
                },
                'current_player': {
                    'size': 14,
                    'color': '#FFFFFF'
                },
                'status_info': {
                    'size': 12,
                    'color': '#CCCCCC'
                },
                'queue_list': {
                    'size': 12,
                    'color': '#E0E0E0'
                },
                'activity_log': {
                    'size': 11,
                    'colors': {
                        'comment_text': '#98FB98',
                        'default_text': '#E0E0E0'
                    }
                },
                'commentary_panel': {
                    'size': 18,
                    'color': '#FFD700'
                }
            },
            'text_formatting': {
                'max_active_name_length': 10,
                'max_active_comment_length': 50,
                'max_history_rewards_length': 50
            },
            'active_players_max_lines': 30
        },
        'commentary_file': '直播词提示.txt'
    }

def test_gui_import():
    """测试GUI模块导入"""
    try:
        from play_displayer import start_displayer_process
        print("✓ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    try:
        config = create_test_config()
        manager = Manager()
        stop_flag = manager.dict()
        stop_flag['running'] = True
        
        # 创建测试队列
        test_queue = MpQueue()
        
        # 测试GUI进程启动包装函数
        from play_main import start_gui_process_wrapper
        
        print("✓ GUI创建函数可用")
        return True
    except Exception as e:
        print(f"✗ GUI创建测试失败: {e}")
        return False

def test_queue_operations():
    """测试队列操作"""
    try:
        # 创建多个队列
        queues = [MpQueue() for _ in range(2)]
        
        # 测试数据
        test_data = {
            'type': 'test_update',
            'message': 'Hello from test'
        }
        
        # 发送到所有队列
        for i, queue in enumerate(queues):
            queue.put(test_data)
            print(f"✓ 队列{i+1}发送成功")
        
        # 从所有队列接收
        for i, queue in enumerate(queues):
            received = queue.get(timeout=1)
            if received == test_data:
                print(f"✓ 队列{i+1}接收成功")
            else:
                print(f"✗ 队列{i+1}数据不匹配")
                return False
        
        # 清理队列
        for queue in queues:
            queue.close()
            queue.join_thread()
        
        return True
    except Exception as e:
        print(f"✗ 队列操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始双GUI界面功能测试...")
    print("=" * 50)
    
    tests = [
        ("GUI模块导入", test_gui_import),
        ("GUI创建功能", test_gui_creation),
        ("队列操作", test_queue_operations),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n测试: {test_name}")
        print("-" * 30)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！双GUI功能应该可以正常工作。")
        print("\n使用方法:")
        print("1. 在配置文件中设置 displayer.num_windows: 2")
        print("2. 运行 python play_main.py")
        print("3. 观察是否出现两个GUI窗口")
    else:
        print("✗ 部分测试失败，请检查代码修改。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
