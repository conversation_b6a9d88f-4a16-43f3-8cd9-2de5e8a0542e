import math
import time
import threading
import logging
import traceback
# --- 关键修改：导入 MutableMapping ---
from typing import Dict, List, Tuple, Any, Optional, Union, cast, MutableMapping
from datetime import datetime
import queue
import copy
import re
import json
import Play_db
import play_db_sync
import Play_obs
import sys
import play_init
import asyncio


logger = logging.getLogger(__name__)
TIME_FORMAT_FOR_COMPARISON = "%a %b %d %H:%M:%S %Y"

# 全量内存订单簿：order_id -> 状态('available','claimed')
orderbook_status: Dict[str, str] = {}
orderbook_status_lock = threading.RLock()

# 全局物体映射缓存：track_number -> class_name
latest_object_map: Dict[str, str] = {}
object_map_lock = threading.Lock()

pending_retry_player = None
pending_retry_player_lock = threading.Lock()

def update_object_map_from_detection(new_object_map: Dict[str, str]) -> bool:
    """
    从检测服务更新物体映射缓存。
    现在只负责更新全局缓存，不再直接操作队列。
    """
    map_changed = False
    
    with object_map_lock:
        if latest_object_map != new_object_map:
            latest_object_map.clear()
            latest_object_map.update(new_object_map)
            map_changed = True
    
    return map_changed

def sync_in_que_with_object_map(in_que: List[Tuple], has_lock: bool = False) -> None:
    """
    同步队列中所有条目的物体名称。
    必须在持有 queue_lock 的情况下调用。
    """
    if not has_lock:
        logger.error("sync_in_que_with_object_map 必须在持有 queue_lock 时调用！")
        return

    updated_count = 0
    
    current_obj_map = {}
    with object_map_lock:
        current_obj_map = latest_object_map.copy()
    
    for queue_item in in_que:
        entry = queue_item[0]
        if len(entry) >= 7:
            target_id_str = str(entry[4])
            current_target_name = entry[6] if len(entry) > 6 else "未知"
            new_target_name = current_obj_map.get(target_id_str, "未知")
            
            if current_target_name != new_target_name:
                if len(entry) == 6:
                    entry.append(new_target_name)
                else:
                    entry[6] = new_target_name
                updated_count += 1
    
    if updated_count > 0:
        logger.debug(f"已同步 {updated_count} 个队列条目的物体名称")

def get_object_name_by_id(target_id: str) -> str:
    """根据目标ID获取物体名称"""
    with object_map_lock:
        return latest_object_map.get(str(target_id), "未知")

def _recalculate_and_update_comments_after_game(
    player_id: str, p_info: Dict[str, Any], p_comments: Dict[str, List[Tuple[str, str]]],
    p_games: Dict[str, List[List[Any]]], current_logger: Optional[logging.Logger]=None,
    pi_lock: Optional[threading.Lock]=None, pc_lock: Optional[threading.Lock]=None, pg_lock: Optional[threading.Lock]=None
):
    """重新计算并更新玩家游戏后评论数，排除上次游戏前已出现的评论内容"""
    if not player_id:
        # 使用默认日志记录器而不是传入的None
        logger_to_use = current_logger if current_logger is not None else logger
        logger_to_use.error("player_id 为空，无法计算有效评论数")
        return
    
    # 使用默认日志记录器
    logger_to_use = current_logger if current_logger is not None else logger

    # 获取历史游戏时间和评论
    last_game_time_str = None
    parsed_last_game_time = None
    comments_for_player = []
    
    # 处理锁可能为None的情况
    has_pc_lock = pc_lock is not None
    has_pg_lock = pg_lock is not None
    
    try:
        if has_pc_lock:
            pc_lock.acquire()
        if has_pg_lock:
            pg_lock.acquire()
            
        history = p_games.get(player_id, [])
        if history:
            candidate = history[-1][0]
            if isinstance(candidate, str) and candidate:
                try:
                    parsed_last_game_time = datetime.strptime(candidate, TIME_FORMAT_FOR_COMPARISON)
                except ValueError:
                    logger_to_use.error(f"无法解析玩家 {player_id} 的上次游戏时间 '{candidate}'")
                    parsed_last_game_time = None
        comments_for_player = list(p_comments.get(player_id, []))
    finally:
        if has_pg_lock:
            pg_lock.release()
        if has_pc_lock:
            pc_lock.release()

    # 构建历史评论内容集合
    historical_contents = set()
    if parsed_last_game_time:
        for dt_str, content in comments_for_player:
            try:
                dt_p = datetime.strptime(dt_str, TIME_FORMAT_FOR_COMPARISON)
                if dt_p <= parsed_last_game_time:
                    historical_contents.add(content)
            except ValueError:
                continue

    # 计算新评论数
    unique_new_contents = set()
    for dt_str, content in comments_for_player:
        try:
            dt_p = datetime.strptime(dt_str, TIME_FORMAT_FOR_COMPARISON)
            if parsed_last_game_time is None:
                unique_new_contents.add(content)
            elif dt_p > parsed_last_game_time and content not in historical_contents:
                unique_new_contents.add(content)
        except ValueError:
            logger_to_use.warning(f"无法解析评论时间 '{dt_str}'，跳过")

    new_count = len(unique_new_contents)

    # 更新 player_info
    if pi_lock is not None:
        with pi_lock:
            if player_id in p_info:
                p_info[player_id]['comments_after_game'] = new_count
            else:
                logger_to_use.warning(f"玩家 {player_id} 在更新时已不在 player_info")
    else:
        # 没有锁，直接更新
        if player_id in p_info:
            p_info[player_id]['comments_after_game'] = new_count
        else:
            logger_to_use.warning(f"玩家 {player_id} 在更新时已不在 player_info")

def calculate_priority(player: str, entry: List, player_info: Dict, player_comments: Dict, 
                      player_games: Dict, config: Dict, current_dt: Optional[str] = None, order_id: Optional[str] = None) -> float:
    """计算玩家优先级系数"""
    priority = 1.0
    # 修正：只解包前6个元素，忽略第7个元素（target_object_name）
    plat, dt, name, player_id, content, head_img = entry[:6]
    priority_config = config.get('game', {}).get('priority', {})

    # 付费乘数
    if order_id is not None:
        priority *= priority_config.get('paid_multiplier', 1.0)

    if player_id not in player_info:
        logger.warning(f"计算优先级时未找到玩家 {player_id} 的信息")
        return priority

    player_data = player_info[player_id]

    # 评论系数
    comments_after_game = player_data.get('comments_after_game', 0)
    pca = priority_config.get('comment', {}).get('pca', 1.0)
    pcb = priority_config.get('comment', {}).get('pcb', 1.0)
    priority *= math.sqrt(pca * (pcb + comments_after_game))

    # 游戏次数系数
    played_times = len(player_games.get(player_id, []))
    ppa = priority_config.get('played_times', {}).get('ppa', 5.0)
    ppb = priority_config.get('played_times', {}).get('ppb', 1.5)
    priority *= ppa * (ppb ** -played_times)

    # 停留时间系数
    come_time_str = player_data.get('come_time', dt)
    try:
        come_time = datetime.strptime(come_time_str, "%a %b %d %H:%M:%S %Y")
        if current_dt:
            current_time_dt = datetime.strptime(current_dt, "%a %b %d %H:%M:%S %Y")
        else:
            current_time_dt = datetime.strptime(dt, "%a %b %d %H:%M:%S %Y")

        time_diff = (current_time_dt - come_time).total_seconds() / 60
        stayed_minutes = max(1, time_diff)
        pta = priority_config.get('stay_time', {}).get('pta', 1.0)
        ptb = priority_config.get('stay_time', {}).get('ptb', 1.0)
        priority *= pta + ptb * math.log(stayed_minutes)
    except Exception:
        pass

    # 奖品状态系数
    prizes = player_games.get(player_id, [])
    has_prize = any(p[1].lower() != 'nothing' for p in prizes)
    prize_status_config = priority_config.get('prize_status', {})
    if has_prize:
        priority *= prize_status_config.get('has_prize', 1.0)
    else:
        priority *= prize_status_config.get('no_prize', 1.5)

    return priority

def calculate_game_possibility(player_id: str, player_info: Dict, player_games: Dict, config: Dict,
                              player_games_lock: Optional[threading.Lock]=None, order_id: Optional[str] = None) -> float:
    """计算玩家游戏请求的可能性系数"""
    # 检查是否为虚拟玩家
    if "VirtualPlayer" in player_id:
        logger.debug(f"玩家 {player_id} 是虚拟玩家，抓中概率为 1.0")
        return 1.0

    possibilities = config.get('game', {}).get('possibilities', {})

    # 处理player_games_lock可能为None的情况
    if player_games_lock is not None:
        with player_games_lock:
            prizes = player_games.get(player_id, [])
    else:
        prizes = player_games.get(player_id, [])

    if not prizes:
        # 首次游戏
        base_possibility = possibilities.get('first_time', 1.0)
    else:
        # 计算历史中奖次数
        win_count = sum(1 for p in prizes if p[1].lower() != 'nothing')

        if win_count == 0:
            # 玩过但从未中奖
            base_possibility = possibilities.get('after_no_prize', 1)
        else:
            # 已中过奖，根据次数从列表中获取概率
            win_probs_list = possibilities.get('after_wins_probabilities', [])

            # 如果新配置不存在，回退到旧配置
            if not win_probs_list:
                base_possibility = possibilities.get('after_prize', 0.0)
                logger.warning(f"玩家 {player_id} 使用旧配置 after_prize，建议更新为 after_wins_probabilities")
            else:
                # 计算索引，如果中奖次数超过列表长度，则使用最后一个概率值
                # win_count 从1开始，列表索引从0开始，所以用 win_count - 1
                prob_index = min(win_count - 1, len(win_probs_list) - 1)
                base_possibility = win_probs_list[prob_index]
                logger.info(f"玩家 {player_id} 已中奖 {win_count} 次，本次游戏基础成功率为: {base_possibility}")

    return base_possibility

def update_queue_priorities_inplace(in_que: List[Tuple], player_info: Dict, player_comments: Dict, 
                                   player_games: Dict, config: Dict, virtual_player_manager: Optional[Any] = None) -> None:
    """
    原地更新队列：
    1. 分离真实玩家和虚拟玩家。
    2. 只对真实玩家进行优先级排序。
    3. 保持虚拟玩家在原位置不变。
    """
    if not in_que:
        return

    # 判断当前模式
    real_players_count = sum(1 for item in in_que if "VirtualPlayer" not in item[0][3])
    min_real_for_fill_mode = config.get('virtual_player', {}).get('min_real_players_in_queue', 2)
    is_filling_mode = real_players_count <= min_real_for_fill_mode

    if is_filling_mode:
        # 填充模式：真实玩家在前，虚拟玩家在后
        real_players = []
        virtual_players = []
        for item in in_que:
            player_id = item[0][3]
            if "VirtualPlayer" in player_id:
                virtual_players.append(item)
            else:
                real_players.append(item)

        # 2. 只对真实玩家计算优先级并排序
        current_dt = time.ctime()
        for i in range(len(real_players)):
            item = real_players[i]
            order_id = item[3] if len(item) == 4 else None
            player_id = item[0][3]
            new_priority = calculate_priority(player_id, item[0], player_info, player_comments, player_games, config, current_dt, order_id)
            
            if order_id is not None:
                real_players[i] = (item[0], item[1], new_priority, order_id)
            else:
                real_players[i] = (item[0], item[1], new_priority)

        # 真实玩家内部排序
        real_players.sort(key=lambda x: (-x[2], x[0][1]))
        
        # 3. 重建队列
        new_queue = []
        new_queue.extend(real_players)
        new_queue.extend(virtual_players)
        
        # --- 处理被提拔玩家的"逐步前提"逻辑 ---
        if virtual_player_manager:
            promoted_vp_index = -1
            for i, item in enumerate(new_queue):
                p_id = item[0][3]
                if "VirtualPlayer" in p_id:
                    vp = virtual_player_manager.active_pool.get(p_id)
                    if vp and vp.is_promoted:
                        promoted_vp_index = i
                        break
            
            if promoted_vp_index > 0:
                # 计算新位置，每次前进2位，但不能超过队首
                new_index = max(0, promoted_vp_index - 2)
                if new_index != promoted_vp_index:
                    # 移动玩家
                    promoted_item = new_queue.pop(promoted_vp_index)
                    new_queue.insert(new_index, promoted_item)
                    logger.info(f"[虚拟玩家-提拔] 玩家 {promoted_item[0][2]} 位置从 {promoted_vp_index + 1} 提升到 {new_index + 1}。")

        # 5. 用重建后的新队列替换旧队列内容
        in_que.clear()
        in_que.extend(new_queue)
    else:
        # 稀疏模式：只对真实玩家排序，虚拟玩家位置保持不变
        # 1. 找出所有真实玩家的索引位置
        real_player_indices = []
        for i, item in enumerate(in_que):
            if "VirtualPlayer" not in item[0][3]:
                real_player_indices.append(i)
        
        # 2. 提取真实玩家并更新优先级
        real_players = []
        for idx in real_player_indices:
            item = in_que[idx]
            order_id = item[3] if len(item) == 4 else None
            player_id = item[0][3]
            new_priority = calculate_priority(player_id, item[0], player_info, player_comments, player_games, config, time.ctime(), order_id)
            
            if order_id is not None:
                real_players.append((item[0], item[1], new_priority, order_id))
            else:
                real_players.append((item[0], item[1], new_priority))
        
        # 3. 对真实玩家排序
        real_players.sort(key=lambda x: (-x[2], x[0][1]))
        
        # 4. 将排序后的真实玩家放回原位置
        for i, idx in enumerate(real_player_indices):
            if i < len(real_players):
                in_que[idx] = real_players[i]

def _handle_game_completion(
    player_id: str, name: str, captured_item: str, order_id: Optional[str],
    config: Dict, current_player: Dict,
    player_info: Dict, player_games: Dict,
    db_sync_manager: Optional[play_db_sync.DBSyncManager],
    status_update_queue: Optional[queue.Queue],
    pi_lock: Optional[threading.Lock]=None, pg_lock: Optional[threading.Lock]=None,
    current_game_start_time: float = 0.0,
    virtual_player_manager: Optional[Any] = None
):
    """
    处理游戏完成后的通用逻辑
    更新玩家统计、数据库、current_player状态，并发送game_end事件
    """
    logger.info(f"[游戏完成] 玩家 {name}({player_id}), 结果: {captured_item}, 订单: {order_id}")

    # --- 核心修改：游戏结束后，根据模式决定如何处理虚拟玩家 ---
    if virtual_player_manager:
        # 判断当前是否为稀疏模式
        real_players_count = len([p for p in virtual_player_manager.in_que if "VirtualPlayer" not in p[0][3]])
        min_real_for_fill_mode = config.get('virtual_player', {}).get('min_real_players_in_queue', 2)
        is_filling_mode = real_players_count <= min_real_for_fill_mode

        if is_filling_mode:
            # 填充模式：如果是真实玩家，增加真实玩家游戏计数，以触发"提拔"
            if "VirtualPlayer" not in player_id:
                virtual_player_manager.increment_real_game_count()
        # 稀疏模式下不再需要特殊处理
    # --- 修改结束 ---

    game_end_time_str = time.ctime()

    # 处理锁可能为None的情况
    if pg_lock:
        with pg_lock:
            if player_id not in player_games:
                player_games[player_id] = []
            player_games[player_id].append([game_end_time_str, captured_item, "N/A", order_id])
    else:
        if player_id not in player_games:
            player_games[player_id] = []
        player_games[player_id].append([game_end_time_str, captured_item, "N/A", order_id])

    if db_sync_manager:
        session_id_val = Play_db.get_current_session_id()
        if session_id_val:
            game_data_for_db = {
                'session_id': session_id_val,
                'player_id': player_id,
                'game_time': game_end_time_str,
                'result': captured_item,
                'order_ID': order_id
            }
            db_sync_manager.add_sync_task("game_update", game_data_for_db)

    # 处理锁可能为None的情况
    if pi_lock:
        with pi_lock:
            if player_id in player_info:
                player_info[player_id]['comments_after_game'] = 0 # 重置评论计数
                if db_sync_manager:
                    db_sync_manager.add_sync_task("player_info_update", {
                        'player_id': player_id,
                        'comments_after_game': 0
                    })
    else:
        if player_id in player_info:
            player_info[player_id]['comments_after_game'] = 0 # 重置评论计数
            if db_sync_manager:
                db_sync_manager.add_sync_task("player_info_update", {
                    'player_id': player_id,
                    'comments_after_game': 0
                })

    # 清除current_player状态
    if current_player.get('player_id') == player_id:
        current_player.clear()
    else:
        logger.warning(f"Game completion for {player_id}, but current_player is {current_player.get('player_id')}")

    # --- 关键修改：简化配置重载逻辑 ---
    # 现在只检查是否有更新，然后把更新任务完全交给 status_update_worker
    try:
        updated_config_sections = play_init.reload_specific_config_sections()
        if updated_config_sections:
            logger.info(f"[游戏完成] 检测到配置文件更新，将发送 'config_reload' 事件。")
            # 发送事件，让专用的 worker 线程去处理更新
            if status_update_queue:
                # 注意：不再传递 full_config，因为它可能已经过时
                status_update_queue.put({
                    'type': 'config_reload',
                    'updated_sections': updated_config_sections
                })
        else:
            logger.debug(f"[游戏完成] 配置文件未修改，跳过配置重新加载")
    except Exception as e:
        logger.error(f"[游戏完成] 检查配置更新时出错: {e}")
    # --- 修改结束 ---

    # 通知游戏结束 - 只发送game_end事件，不再发送caught事件
    if status_update_queue:
        player_info_copy = {}
        if pi_lock:
            with pi_lock:
                player_info_copy = copy.deepcopy(player_info)
        else:
            player_info_copy = copy.deepcopy(player_info)

        status_update_queue.put({
            'type': 'game_end',
            'player_id': player_id,
            'name': name,
            'result': captured_item,
            'player_info': player_info_copy,
            'duration': time.time() - current_game_start_time
        })
        # 移除重复发送的caught事件，因为已经在play_logic.go_play中发送
        # 这里仅发送游戏结束事件用于状态管理

def process_game_queue(in_que: List[Tuple], player_info: Dict, player_comments: Dict, player_games: Dict, 
                      queue_lock: threading.Lock, 
                      stop_flag: MutableMapping[str, bool], 
                      current_player: Dict, 
                      config: Dict, status_update_queue: Optional[queue.Queue] = None,
                      pi_lock: Optional[threading.Lock] = None, pc_lock: Optional[threading.Lock] = None, pg_lock: Optional[threading.Lock] = None,
                      move_service_client = None, virtual_player_manager: Optional[Any] = None,
                      detect_service_connected_event: Optional[threading.Event] = None): # <-- 新增参数
    """游戏处理线程：从队列中提取优先级最高的玩家进行游戏处理"""
    thread_config = config.get('game_thread', {})
    empty_queue_sleep = thread_config.get('empty_queue_sleep', 0.5)
    post_game_sleep = thread_config.get('post_game_sleep', 0.5)
    error_sleep = thread_config.get('error_sleep', 1.0)
    game_timeout = thread_config.get('game_timeout', 30)
    global pending_retry_player
    import play_db_sync
    db_sync_manager = play_db_sync.db_sync_manager

    # --- 修改：将循环条件改为True，并将停止检查移入循环体 ---
    while True:
        try:
            if not stop_flag.get('running', True):
                logger.info("[游戏线程] 收到停止信号，线程退出。")
                break
        except (EOFError, BrokenPipeError):
            logger.info("[游戏线程] 共享状态连接已关闭，线程退出。")
            break
        # --- 修改结束 ---

        # --- 新增：在处理任何玩家前，检查关键服务是否就绪 ---
        if detect_service_connected_event and not detect_service_connected_event.is_set():
            logger.warning("[游戏线程] 检测服务已断开，暂停处理新游戏，等待其恢复...")
            detect_service_connected_event.wait(timeout=2.0) # 等待2秒，避免日志刷屏
            continue
        # --- 新增结束 ---

        # 优先处理pending_retry_player
        entry_tuple = None
        logger.debug(f"[DEBUG] 当前队列长度: {len(in_que)}, current_player: {current_player}, pending_retry_player: {pending_retry_player}")
        with pending_retry_player_lock:
            if pending_retry_player:
                entry_tuple = pending_retry_player['entry_tuple']
                current_player.update(pending_retry_player['current_player'])
                pending_retry_player = None
                logger.info(f"[重试机制] 检测到pending_retry_player，优先重试玩家: {entry_tuple[0][2]}({entry_tuple[0][3]})")
        lock_acquired = False
        latest_queue_copy = []
        if not entry_tuple:
            try:
                lock_acquired = queue_lock.acquire(timeout=1.0)
                if lock_acquired:
                    if in_que:
                        # 检查是否有游戏正在进行
                        if current_player.get('status') == 'waiting_move_service':
                            logger.debug("[游戏线程] 当前有游戏正在等待移动服务响应，暂不处理新请求。")
                            queue_lock.release()
                            lock_acquired = False
                            time.sleep(config.get('game_thread', {}).get('empty_queue_sleep', 0.5))
                            continue
                        
                        # 遍历队列寻找符合间隔条件的玩家
                        min_interval = config.get('game', {}).get('min_interval_seconds', 120)
                        eligible_player_found = False
                        
                        for i, item in enumerate(in_que):
                            player_id_to_check = item[0][3]  # 获取玩家ID
                            player_name_to_check = item[0][2]  # 获取玩家名称
                            
                            # 检查玩家的最后游戏时间
                            last_play_time_str = None
                            if player_id_to_check in player_games and player_games.get(player_id_to_check):
                                last_play_time_str = player_games[player_id_to_check][-1][0]
                                
                            if last_play_time_str:
                                try:
                                    last_time = datetime.strptime(last_play_time_str, TIME_FORMAT_FOR_COMPARISON)
                                    current_time = datetime.now()
                                    time_diff = (current_time - last_time).total_seconds()
                                    
                                    if time_diff < min_interval:
                                        logger.debug(f"玩家 {player_name_to_check}({player_id_to_check}) 在队列位置 {i+1}，"
                                                   f"游戏间隔不足 ({time_diff:.0f}s < {min_interval}s)，本轮跳过")
                                        continue  # 跳过此玩家，检查下一个
                                        
                                except ValueError as e:
                                    logger.warning(f"解析玩家 {player_name_to_check} 的上次游戏时间出错: {e}，按可玩处理")
                            
                            # 执行到这里说明玩家可以游戏
                            logger.info(f"在队列位置 {i+1} 找到符合游戏间隔条件的玩家: {player_name_to_check}")
                            entry_tuple = in_que.pop(i)  # 从队列中取出该玩家
                            eligible_player_found = True
                            latest_queue_copy = list(in_que)  # 保存队列快照
                            break  # 找到后退出循环
                            
                        if not eligible_player_found:
                            logger.debug("[游戏线程] 当前队列中所有玩家均不满足游戏间隔条件，等待下一轮")
                            queue_lock.release()
                            lock_acquired = False
                            time.sleep(empty_queue_sleep)
                            continue
                        
                    else:
                        logger.debug("[游戏线程] 当前队列为空")
                        queue_lock.release()
                        lock_acquired = False
                        time.sleep(empty_queue_sleep)
                        continue
                else:
                    logger.warning("[游戏线程] 获取队列锁超时")
                    time.sleep(empty_queue_sleep)
                    continue
            finally:
                if lock_acquired:
                    queue_lock.release()
                    if entry_tuple and status_update_queue:
                        player_info_copy = None
                        if pi_lock:
                            with pi_lock:
                                player_info_copy = copy.deepcopy(player_info)
                        else:
                            player_info_copy = copy.deepcopy(player_info)
                        status_update_queue.put({
                            'type': 'queue_update',
                            'in_que': latest_queue_copy,
                            'player_info': player_info_copy
                        })
        if not entry_tuple:
            time.sleep(config.get('game_thread', {}).get('empty_queue_sleep', 0.5))
            continue

        # 解包队列项
        entry_list = entry_tuple[0]
        possibility = entry_tuple[1]
        order_id = entry_tuple[3] if len(entry_tuple) == 4 else None

        # 游戏处理逻辑
        current_game_start_time = time.time()
        try:
            entry_data = entry_list 
            name = entry_data[2]
            player_id = entry_data[3]
            target_id_from_comment = entry_data[4]
            
            # 确保锁已初始化
            if not pi_lock or not pg_lock:
                logger.error(f"锁未初始化，无法为玩家 {name} 处理游戏。")
                time.sleep(error_sleep)
                continue

            # 在游戏真正开始前扣除免费次数（如果没有订单）
            with pi_lock:
                if order_id is None:
                    free_games_used = player_info.get(player_id, {}).get('free_games_used_this_session', 0)
                    if player_id in player_info:
                        player_info[player_id]['free_games_used_this_session'] = free_games_used + 1
                        logger.info(f"[游戏线程] 玩家 {name}({player_id}) 开始免费游戏，扣除免费次数 ({free_games_used + 1})")
                        
                        if db_sync_manager:
                            db_sync_manager.add_sync_task("update_player_order_state", {
                                'player_id': player_id,
                                'free_games_used_this_session': player_info[player_id]['free_games_used_this_session']
                            })
                else:
                    logger.info(f"[游戏线程] 玩家 {name}({player_id}) 使用订单 {order_id}，不消耗免费次数")

            # 更新当前玩家状态
            current_player.update({
                'player_id': player_id, 'name': name, 'start_time': current_game_start_time,
                'target_id': target_id_from_comment, 'target_object': "未知", 'order_id': order_id
            })
            
            # 获取目标物体名称
            current_player['target_object'] = get_object_name_by_id(target_id_from_comment)
            
            # 通知游戏开始
            if status_update_queue is not None:
                # --- 在 game_start 事件中加入历史奖励数据 ---
                player_history = []
                if pg_lock:
                    with pg_lock:
                        # 深拷贝以确保线程安全
                        player_history = copy.deepcopy(player_games.get(player_id, []))

                status_update_queue.put({
                    'type': 'game_start', 'player_id': player_id, 'name': name,
                    'start_time': current_player['start_time'], 'target_id': target_id_from_comment,
                    'target_object': current_player['target_object'],
                    'history_for_player': player_history  # 新增字段
                })
                # --- 结束 ---

            # 使用移动服务进行游戏
            if not move_service_client:
                logger.error(f"[游戏线程] 移动服务客户端未初始化，玩家 {name}({player_id}) 游戏失败")
                _handle_game_completion(
                    player_id, name, "nothing", order_id, config, current_player,
                    player_info, player_games, db_sync_manager, status_update_queue,
                    pi_lock, pg_lock, current_game_start_time, virtual_player_manager
                )
                time.sleep(error_sleep)
                continue

            # 设置等待移动服务状态
            current_player['status'] = 'waiting_move_service'

            # 根据概率判断结果决定 z_offset_extra 的值
            import random

            # 对于虚拟玩家，确保100%成功
            if "VirtualPlayer" in player_id:
                should_succeed = True
                z_offset_extra = 0.0
                logger.info(f"[游戏线程] 虚拟玩家 {name}({player_id}) 确保抓中, z_offset_extra=0")
            else:
                should_succeed = random.random() < possibility
                if should_succeed:
                    z_offset_extra = 0.0  # 能抓中时发送0
                    logger.info(f"[游戏线程] 玩家 {name}({player_id}) 概率判断: 应该抓中 (概率={possibility:.3f}), z_offset_extra=0")
                else:
                    z_offset_extra = config.get('game', {}).get('z_offset_extra', 10.0)  # 抓不中时发送配置值
                    logger.info(f"[游戏线程] 玩家 {name}({player_id}) 概率判断: 应该抓不中 (概率={possibility:.3f}), z_offset_extra={z_offset_extra}")

            # 发送抓取指令到移动服务
            command_id = move_service_client.send_pick_command(
                target_object_id=target_id_from_comment,
                player_id=player_id,
                player_name=name,
                z_offset_extra=z_offset_extra
            )
            
            if command_id:
                current_player['move_command_id'] = command_id
                logger.info(f"[游戏线程] 玩家 {name}({player_id}) 抓取指令已发送到移动服务，命令ID: {command_id}")
                # 游戏结果将通过移动服务事件返回，这里不需要继续处理
            else:
                logger.error(f"[游戏线程] 玩家 {name}({player_id}) 发送抓取指令失败，移动服务端可能已断开，当前玩家将进入pending_retry_player，等待服务端恢复后重试。")
                # --- 新增：只暂存当前玩家，不直接判定失败 ---
                # global pending_retry_player
                with pending_retry_player_lock:
                    pending_retry_player = {
                        'entry_tuple': (
                            entry_list,
                            possibility,
                            10.0,  # priority可用默认值
                            order_id
                        ),
                        'current_player': current_player.copy()
                    }
                current_player.clear()
                # 这里直接return，等待服务端恢复后自动重试
                time.sleep(error_sleep * 3)  # 延长重试间隔，避免疯狂重试
                continue

            time.sleep(post_game_sleep)

        except Exception as e:
            logger.error(f"处理游戏时发生未预期的错误: {e}")
            logger.error(traceback.format_exc())
            current_player.clear()
            if status_update_queue is not None:
                status_update_queue.put({'type': 'game_error', 'error': str(e)})
            time.sleep(error_sleep)

def process_batch_game_requests(new_game_requests: List[Tuple[str, List]], player_info: Dict, player_comments: Dict, 
                              player_games: Dict, in_que: List, queue_lock: threading.Lock, config: Dict, 
                              current_player: Dict, db_sync_manager=None, player_games_lock: Optional[threading.Lock] = None,
                              status_update_queue=None,
                              virtual_player_manager=None) -> Tuple[List, List]:
    """批量处理游戏请求"""
    if not new_game_requests:
        return [], []

    processed_player_ids = []
    prepared_requests_to_queue = []
    current_player_id = current_player.get('player_id')
    queue_updated = False  # 添加标志来跟踪队列是否有更新

    # 处理player_games_lock为None的情况
    safe_player_games_lock = player_games_lock if player_games_lock is not None else threading.Lock()

    # 批次内去重
    unique_requests = {}
    for player_id, entry in new_game_requests:
        if player_id not in unique_requests:
            unique_requests[player_id] = entry
        else:
            existing_entry = unique_requests[player_id]
            try:
                existing_time = time.mktime(time.strptime(existing_entry[1]))
                new_time = time.mktime(time.strptime(entry[1]))
                if new_time < existing_time:
                    unique_requests[player_id] = entry
            except Exception:
                pass

    # 获取队列快照
    queued_players = []
    lock_acquired = False
    try:
        lock_acquired = queue_lock.acquire(timeout=3)
        if lock_acquired:
            queued_players = [item[0][3] for item in in_que]
    finally:
        if lock_acquired:
            queue_lock.release()

    # 处理请求
    min_interval = config.get('game', {}).get('min_interval_seconds', 120)
    max_free_games = config.get('game', {}).get('max_free_games_per_session', 1)
    
    for player_id, entry in unique_requests.items():
        # 统一使用 [:6] 截取，确保兼容6或7元素的entry
        plat, msg_dt, name, _, content, head_img = entry[:6]
        
        # 检查规则
        skip_reason = None
        if player_id in processed_player_ids:
            skip_reason = "同批次已处理"
        elif player_id in queued_players:
            # 如果已在队列中，但本次请求的目标编号与原来不同，则更新该队列项
            with queue_lock:
                for idx, queue_item in enumerate(in_que):
                    entry_q = queue_item[0]   # [plat, dt, name, player_id, content, head_img, target_object_name]
                    if entry_q[3] == player_id:
                        old_content = entry_q[4]
                        if old_content != content:
                            logger.info(f"玩家 {name}({player_id}) 修改排队目标: {old_content} -> {content}")
                            # 替换内容和时间，并更新物体名称
                            entry_q[4] = content
                            entry_q[1] = msg_dt
                            # 获取新的物体名称
                            new_target_name = get_object_name_by_id(content)
                            if len(entry_q) == 6:
                                entry_q.append(new_target_name)
                            else:
                                entry_q[6] = new_target_name
                            queue_updated = True  # 标记队列已更新
                        break
            skip_reason = "已在队列中（目标已更新）"
        elif player_id == current_player_id:
            skip_reason = "正在游戏中"
        
        if skip_reason:
            logger.info(f"玩家 {name}({player_id}) 请求被跳过: {skip_reason}")
            processed_player_ids.append(player_id)
            continue
        
        # 检查订单和免费游戏：只看是否有已验证过的订单号
        order_id_to_use = None
        temp_his_order_id = player_info[player_id].get('temp_his_orderID_toUse')
        free_games_used = player_info[player_id].get('free_games_used_this_session', 0)

        if temp_his_order_id:
            # —— 优先：已验证历史订单 —— 
            order_id_to_use = temp_his_order_id
            player_info[player_id]['temp_his_orderID_toUse'] = None
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 使用已验证订单 {temp_his_order_id} 加入队列")
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_his_orderID_toUse': None
                })
        elif free_games_used < max_free_games:
            # —— 普通免费名额 —— 
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费游戏请求加入队列 ({free_games_used}/{max_free_games})")
        else:
            # —— 免费次数用尽，存为待处理 —— 
            player_info[player_id]['pending_game_entry_details'] = entry
            pending_json = json.dumps(entry, ensure_ascii=False)
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费次数用尽，存储待验证游戏请求")
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'pending_game_entry_details_json': pending_json
                })

            # 通知webdisplay显示等待订单提示
            if status_update_queue is not None:
                status_update_queue.put({
                    'type': 'waiting_order',
                    'player_id': player_id,
                    'player_name': name
                })

            processed_player_ids.append(player_id)
            continue

        # 获取物体名称
        target_object_name = get_object_name_by_id(content)
        
        # 创建带物体名称的entry（7个字段）
        entry_with_object_name = [plat, msg_dt, name, player_id, content, head_img, target_object_name]

        # 计算可能性和优先级
        possibility = calculate_game_possibility(player_id, player_info, player_games, config, safe_player_games_lock, order_id_to_use)
        priority = calculate_priority(player_id, entry_with_object_name, player_info, player_comments, player_games, config, order_id=order_id_to_use)
        
        if order_id_to_use is not None:
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 带订单 {order_id_to_use} 加入队列，目标: {content}({target_object_name})，优先级: {priority:.2f}")
            prepared_requests_to_queue.append((entry_with_object_name, possibility, priority, order_id_to_use))
        else:
            logger.info(f"[游戏请求] 玩家 {name}({player_id}) 免费游戏加入队列，目标: {content}({target_object_name})，优先级: {priority:.2f}")
            prepared_requests_to_queue.append((entry_with_object_name, possibility, priority, None))
            
        processed_player_ids.append(player_id)

    # 修改此处的判断条件 - 即使没有新请求，但队列有更新也要处理
    if not prepared_requests_to_queue and not queue_updated:
        logger.debug("[游戏请求] 批量处理完成，队列无实际变化。")
        return [], processed_player_ids
    
    # 更新队列
    updated_queue_snapshot = []
    lock_acquired = False
    new_players_added = False # 标记是否有新玩家加入
    try:
        lock_acquired = queue_lock.acquire(timeout=5)
        if lock_acquired:
            if prepared_requests_to_queue:  # 只有当有新请求时才添加
                in_que.extend(prepared_requests_to_queue)
                new_players_added = True # 标记有新玩家加入
            
            # 只要有新请求加入 或 现有请求更新，就进行重排序
            update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config, virtual_player_manager)
            
            # --- 核心改动：如果添加了新玩家，则触发按需检查 ---
            if new_players_added and virtual_player_manager:
                # 调用按需检查，传递already_has_lock=True，表示我们已持有锁
                virtual_player_manager.check_and_inject_player_on_demand(already_has_lock=True)

            updated_queue_snapshot = copy.deepcopy(in_que)

            # 改进日志记录
            log_message_suffix = f"当前队列长度: {len(in_que)}"
            if prepared_requests_to_queue and queue_updated:
                logger.info(f"[游戏请求] 批量处理完成，{len(prepared_requests_to_queue)} 个新请求已加入，且现有条目已更新。{log_message_suffix}")
            elif prepared_requests_to_queue:
                logger.info(f"[游戏请求] 批量处理完成，{len(prepared_requests_to_queue)} 个新请求已加入。{log_message_suffix}")
            elif queue_updated:
                logger.info(f"[游戏请求] 批量处理完成，队列中现有条目目标已更新并重排。{log_message_suffix}")
            
            # 发送队列更新事件
            if status_update_queue is not None:
                player_info_copy = copy.deepcopy(player_info)
                data_for_status_event = {
                    'type': 'queue_update',
                    'in_que': updated_queue_snapshot,
                    'player_info': player_info_copy
                }
                status_update_queue.put(data_for_status_event)
                
    except Exception as e:
        logger.error(f"更新队列时出错: {e}")
    finally:
        if lock_acquired:
            queue_lock.release()
    
    if db_sync_manager and (prepared_requests_to_queue or queue_updated):
        current_session_id = Play_db.get_current_session_id()
        db_sync_manager.add_sync_task("final_queue_update", (current_session_id, updated_queue_snapshot))
    
    return updated_queue_snapshot, processed_player_ids

def process_order_number(player_id: str, order_number: str, player_name: str, config: Dict, 
                         player_info: Dict, in_que: List,
                         player_info_lock: threading.Lock, queue_lock: threading.Lock, 
                         db_sync_manager=None,
                         player_comments_lock: Optional[threading.Lock]=None, player_games_lock: Optional[threading.Lock]=None,
                         status_update_queue=None, player_comments: Optional[Dict]=None, player_games: Optional[Dict]=None) -> None:
    """处理评论中检测到的订单号 - 使用状态管理模式"""
    logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 发送订单号: {order_number}")
    
    # 处理可能为None的参数
    safe_player_comments = {} if player_comments is None else player_comments
    safe_player_games = {} if player_games is None else player_games
    
    with player_info_lock, orderbook_status_lock:
        if player_id not in player_info:
            # 修正：移除对未定义的current_logger的引用
            logger.error(f"[订单处理] 玩家 {player_name}({player_id}) 信息不存在，无法处理订单 {order_number}")
            return

        # 检查订单状态
        status = orderbook_status.get(order_number)
        
        if status == 'available':
            logger.info(f"[订单处理] 订单 {order_number} 可用，分配给玩家 {player_name}({player_id})")
            # 1) 内存先行：更新内存中的订单状态
            orderbook_status[order_number] = 'claimed'
            player_info[player_id]['temp_order_toVerify'] = None

            # 2) 异步落库：触发数据库更新
            if db_sync_manager:
                # 更新数据库中的订单状态
                db_sync_manager.add_sync_task("update_pending_order_status", {
                    'order_id': order_number,
                    'status': 'claimed'
                })
                # 清理验证字段
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_order_toVerify': None
                })
            
            logger.info(f"[订单处理] 订单 {order_number} 已被玩家 {player_name}({player_id}) 成功领取")

            pending_entry_original = player_info[player_id].get('pending_game_entry_details')  # This is 6 elements
            if pending_entry_original:
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 有待处理的游戏请求，将使用订单 {order_number} 立即加入队列")
                player_info[player_id]['pending_game_entry_details'] = None
                
                if db_sync_manager:
                    db_sync_manager.add_sync_task("update_player_order_state", {
                        'player_id': player_id,
                        'pending_game_entry_details_json': None
                    })

                # Extract content for object name lookup - 确保安全解包
                pending_content = pending_entry_original[4]  # 直接使用索引而不是解包
                target_object_name_for_pending = get_object_name_by_id(pending_content)
                
                # Create a 7-element entry for the queue
                entry_for_queue_from_pending = pending_entry_original + [target_object_name_for_pending]

                # 计算优先级并加入队列
                if player_comments_lock and player_games_lock and player_comments is not None and player_games is not None:
                    with player_comments_lock, player_games_lock:
                        possibility = calculate_game_possibility(player_id, player_info, player_games, config, player_games_lock, order_id=order_number)
                        # Pass the 7-element entry to calculate_priority
                        priority = calculate_priority(player_id, entry_for_queue_from_pending, player_info, player_comments, player_games, config, order_id=order_number)
                else:
                    possibility = 0.5
                    priority = 10.0
                
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 使用订单 {order_number} 进入队列，目标: {pending_content}({target_object_name_for_pending})，优先级: {priority:.2f}")
                
                with queue_lock:
                    # Add the 7-element entry to the queue
                    in_que.append((entry_for_queue_from_pending, possibility, priority, order_number))
                    if player_comments is not None and player_games is not None:
                        update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
                
                if status_update_queue:
                    status_update_queue.put({
                        'type': 'queue_update',
                        'in_que': copy.deepcopy(in_que),
                        'player_info': copy.deepcopy(player_info)
                    })
            else:
                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 无待处理游戏请求，检查是否可以升级现有队列项目")
                # 检查是否已在队列并升级
                with queue_lock:
                    player_in_queue_idx = -1
                    for i, queue_item in enumerate(in_que):
                        if len(queue_item) >= 3:
                            entry_q = queue_item[0]
                            if entry_q[3] == player_id:
                                has_order_id = len(queue_item) == 4 and queue_item[3] is not None
                                if not has_order_id:
                                    player_in_queue_idx = i
                                    logger.info(f"[订单处理] 找到玩家 {player_name}({player_id}) 在队列中的位置: {i+1}，准备升级为付费")
                                    break

                    if player_in_queue_idx != -1:
                        if player_comments_lock and player_games_lock and player_comments is not None and player_games is not None:
                            with player_comments_lock, player_games_lock:
                                entry_q, possibility_q, old_priority = in_que[player_in_queue_idx]
                                new_priority_q = calculate_priority(player_id, entry_q, player_info, player_comments, player_games, config, order_id=order_number)
                                in_que[player_in_queue_idx] = (entry_q, possibility_q, new_priority_q, order_number)
                                update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
                                logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 队列项已升级为付费: {old_priority:.2f} -> {new_priority_q:.2f} (使用订单 {order_number})")
                        
                        queue_snapshot = copy.deepcopy(in_que)
                        
                        if status_update_queue:
                            status_update_queue.put({
                                'type': 'queue_update',
                                'in_que': queue_snapshot,
                                'player_info': copy.deepcopy(player_info)
                            })
                
                if player_in_queue_idx == -1:
                    logger.info(f"[订单处理] 玩家 {player_name}({player_id}) 不在队列中，将订单 {order_number} 存储为 temp_his_orderID_toUse")
                    player_info[player_id]['temp_his_orderID_toUse'] = order_number
                    if db_sync_manager:
                        db_sync_manager.add_sync_task("update_player_order_state", {
                            'player_id': player_id,
                            'temp_his_orderID_toUse': order_number
                        })
                        
        elif status == 'claimed':
            logger.warning(f"[订单处理] 订单 {order_number} 状态为 {status}，已被其他玩家使用，无法重复认领")
            # 可以选择通知玩家或采取其他措施
            return
        else:
            # 订单尚未抓到或未知状态，按原逻辑处理为待验证
            logger.info(f"[订单处理] 订单 {order_number} 未知或状态异常，存储为 temp_order_toVerify 等待验证")
            player_info[player_id]['temp_order_toVerify'] = order_number
            
            if db_sync_manager:
                db_sync_manager.add_sync_task("update_player_order_state", {
                    'player_id': player_id,
                    'temp_order_toVerify': order_number,
                    # 'pending_game_entry_details_json': None 这里不要删除原请求，等待订单验证即可。
                })

def extract_order_number(content: str) -> Optional[str]:
    """从内容中提取潜在的订单号（连续的12位以上数字）"""
    if not content:
        return None
    
    # 使用正则表达式查找连续的12位以上数字
    pattern = r'\d{12,}'
    match = re.search(pattern, content)
    
    if match:
        return match.group(0)
    return None

def process_messages_batch(msg_gen, config, session_id, player_info, player_comments, player_games, 
                          in_que, queue_lock, current_player, 
                          # --- 关键修改：修改类型注解 ---
                          stop_flag: MutableMapping[str, bool], 
                          status_update_queue,
                          player_info_lock, player_comments_lock, player_games_lock,
                          virtual_player_manager=None):
    """批量处理消息的主函数"""
    import play_init
    import play_db_sync
    
    batch_start_time = time.time()

    for msg in msg_gen:
        if not stop_flag.get('running', True):
            break
            
        # 处理心跳消息
        if isinstance(msg, dict) and msg.get('type') == 'Heartbeat':
            with queue_lock, player_info_lock, player_comments_lock, player_games_lock:
                update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
            
            # 只在队列非空时才发送更新事件，避免频繁发送空队列更新
            if in_que:
                with player_info_lock:
                    player_info_copy = copy.deepcopy(player_info)
                status_update_queue.put({
                    'type': 'queue_update',
                    'in_que': in_que.copy(),
                    'player_info': player_info_copy
                })
            
            current_time = time.time()
            if current_time - batch_start_time > 5:
                batch_start_time = current_time
            continue
        
        # 常规消息处理
        if isinstance(msg, dict) and "message" in msg and isinstance(msg["message"], list):
            messages = msg["message"]
            batch_game_requests = []
            batch_comments = []
            
            for message in messages:
                dt = message.get('time', time.ctime())
                # parse_message 返回恰好6个元素，这里不需要修改
                plat, msg_dt, name, player_id, content, head_img = play_init.parse_message(message, dt)
                
                if not player_id and not name:
                    continue
                
                # 修正：处理新玩家时移除 paid 字段
                with player_info_lock:
                    if player_id not in player_info:
                        player_info[player_id] = {
                            'plat': plat, 'name': name, 'player': player_id, 'head_img': head_img,
                            'come_time': msg_dt,  # 设置首次出现时间
                            'comments_after_game': 0,
                            'temp_order_toVerify': None, 'temp_his_orderID_toUse': None,
                            'pending_game_entry_details': None, 'free_games_used_this_session': 0
                        }
                        # 使用新的专门同步任务确保玩家记录立即添加到数据库
                        if play_db_sync.db_sync_manager:
                            player_data_for_db = {
                                'player_id': player_id,
                                'plat': plat,
                                'name': name,
                                'head_img': head_img,
                                'come_time': msg_dt,
                                'comments_after_game': 0,
                                'temp_order_toVerify': None,
                                'temp_his_orderID_toUse': None,
                                'pending_game_entry_details': None,
                                'free_games_used_this_session': 0
                            }
                            play_db_sync.db_sync_manager.add_sync_task("add_new_player", player_data_for_db)
                    else:
                        # 更新玩家姓名（如果有变化）
                        if player_info[player_id]['name'] != name:
                            player_info[player_id]['name'] = name
                        # 如果come_time为空或未设置，则设置为当前消息时间
                        if not player_info[player_id].get('come_time'):
                            player_info[player_id]['come_time'] = msg_dt
                
                if content:
                    is_game_request = bool(re.fullmatch(r'\d{1,2}', content))
                    extracted_order_number = extract_order_number(content)
                    
                    # entry 在这里就是6个元素，符合预期
                    with player_info_lock:
                        entry = [plat, msg_dt, name, player_id, content, head_img]
                    
                    if is_game_request:
                        logger.info(f"[消息处理] 玩家 {name}({player_id}) 发送游戏请求: {content}")
                        batch_game_requests.append((player_id, entry))
                    elif extracted_order_number:
                        logger.info(f"[消息处理] 玩家 {name}({player_id}) 发送包含订单号的消息: {content}，提取订单号: {extracted_order_number}")
                        process_order_number(
                            player_id, extracted_order_number, name, config, player_info, in_que,
                            player_info_lock, queue_lock, play_db_sync.db_sync_manager,
                            player_comments_lock, player_games_lock, status_update_queue,
                            player_comments, player_games
                        )
                    else:
                        batch_comments.append((player_id, name, msg_dt, content))
            
            # 处理评论
            if batch_comments:
                with player_comments_lock:
                    for player_id, name, msg_dt, content in batch_comments:
                        if player_id not in player_comments:
                            player_comments[player_id] = []
                        if [msg_dt, content] not in player_comments[player_id]:
                            player_comments[player_id].append([msg_dt, content])
                            if play_db_sync.db_sync_manager:
                                play_db_sync.db_sync_manager.add_sync_task("comment_update", (player_id, msg_dt, content))
                             # 获取总评论数和总游戏次数
                            total_comments_count = len(player_comments.get(player_id, []))
                            total_games_count = 0
                            with player_games_lock:
                                total_games_count = len(player_games.get(player_id, []))
                            
                            # --- 修改开始：计算并添加停留时长 ---
                            stay_duration_min = 1
                            with player_info_lock:
                                come_time_str = player_info.get(player_id, {}).get('come_time')
                            if come_time_str:
                                try:
                                    come_time = datetime.strptime(come_time_str, TIME_FORMAT_FOR_COMPARISON)
                                    stay_duration_min = max(1, int((datetime.now() - come_time).total_seconds() / 60))
                                except (ValueError, TypeError):
                                    pass
                            # --- 修改结束 ---

                            status_update_queue.put({
                                'type': 'new_comment', 'player_id': player_id,
                                'player_name': name,
                                'comment_text': content, 'comment_dt': msg_dt,
                                'total_comments': total_comments_count,
                                'total_games': total_games_count,
                                'stay_duration_min': stay_duration_min # 停留时长（分钟）
                            })
                
                for player_id, name, msg_dt, content in batch_comments:
                    _recalculate_and_update_comments_after_game(
                        player_id, player_info, player_comments, player_games, logger,
                        player_info_lock, player_comments_lock, player_games_lock
                    )
                
            # 处理游戏请求
            if batch_game_requests:
                updated_queue_snapshot, processed_players = process_batch_game_requests(
                    batch_game_requests, player_info, player_comments, player_games,
                    in_que, queue_lock, config, current_player, play_db_sync.db_sync_manager, 
                    player_games_lock, status_update_queue, virtual_player_manager
                )
                # 注意：现在 process_batch_game_requests 内部已经发送了 queue_update 事件
                logger.debug(f"[消息处理] 批量游戏请求处理完成，处理了 {len(processed_players)} 个玩家")

def handle_status_updates_worker(
    status_update_queue, gui_update_queues,  # 修改为支持多个GUI队列
    stop_flag: MutableMapping[str, bool],
    player_comments, player_games, player_info, current_player, config,
    db_sync_manager, player_comments_lock, player_games_lock,
    player_info_lock, move_service_client, web_display=None,
    virtual_player_manager=None,
    update_global_config_func=None, queue_lock=None, in_que=None,
    detect_service_connected_event: Optional[threading.Event] = None):
    """处理状态更新队列中的更新"""
    global pending_retry_player
    import Play_obs
    import queue
    import traceback
    import datetime
    import json

    # --- 新增：启动时，假设检测服务是连接的，直到收到断开通知 ---
    if detect_service_connected_event:
        detect_service_connected_event.set()
    # --- 新增结束 ---

    # 确保 Play_obs.obs_controller 在使用前已初始化
    try:
        if Play_obs.obs_controller is None and config.get('obs', {}).get('enabled', True):
             Play_obs.init_obs_controller(config)
        obs_controller = Play_obs.obs_controller
    except Exception as e:
        logger.error(f"在 handle_status_updates_worker 中初始化OBS控制器失败: {e}")
        obs_controller = None
    
    def prepare_queue_data_for_web(in_que, player_info, config):
        """准备队列数据用于Web显示"""
        # 这个函数现在只负责格式化，动态计算移到主循环
        web_config = config.get('web_display', {})
        queue_config = web_config.get('queue_display', {})
        
        if not queue_config.get('enabled', False):
            return None
        
        max_items = queue_config.get('max_items', 8)
        headers_config = queue_config.get('headers', {})
        
        queue_items = []
        for i, queue_item in enumerate(in_que[:max_items]):
            try:
                if len(queue_item) == 4:
                    entry, possibility, priority, order_id = queue_item
                else:
                    entry, possibility, priority = queue_item
                    order_id = None
                
                plat, dt, name, player_id, content, head_img = entry[:6]
            except (ValueError, IndexError, TypeError) as e:
                logger.error(f"解包队列项目时出错: {e}, queue_item: {queue_item}")
                continue
            
            # 计算停留时间
            time_min = 1
            try:
                if player_id in player_info and 'come_time' in player_info[player_id]:
                    come_time_str = player_info[player_id]['come_time']
                    come_time = datetime.datetime.strptime(come_time_str, "%a %b %d %H:%M:%S %Y")
                    current_time = datetime.datetime.now()
                    time_diff = (current_time - come_time).total_seconds() / 60
                    time_min = max(1, int(time_diff))
            except Exception as e:
                logger.error(f"计算玩家 {player_id} 停留时间出错: {e}")
            
            # 获取游戏后评论数
            comments_count = 0
            try:
                if player_id in player_info and 'comments_after_game' in player_info[player_id]:
                    comments_count = player_info[player_id]['comments_after_game']
            except Exception as e:
                logger.error(f"获取玩家 {player_id} 游戏后评论数出错: {e}")
            
            # 获取当前名称
            current_name = player_info.get(player_id, {}).get('name', name)
            
            queue_items.append({
                'index': i + 1,
                'name': current_name,
                'comments_count': comments_count,
                'time_min': time_min,
                'order_id': order_id
            })
        
        # 处理页脚文本的换行符 - 将 \n 转换为 <br>
        footer_text_raw = headers_config.get('text_after', '加关注、评论多、停留长的家人优先！\n重复评论不算；玩过后评论重新计数')
        footer_text_html = footer_text_raw.replace('\n', '<br>')
        
        return {
            'items': queue_items,
            'header_text': headers_config.get('text_before', '排队网友'),
            'footer_text': footer_text_html,
            'show_headers': headers_config.get('show', True),
            'headers': headers_config.get('columns', {
                'index': 'No.',
                'name': '家人',
                'comments': '评论数',
                'time': '时长'
            })
        }
    
    # --- 修改：将循环条件改为True，并将停止检查移入循环体 ---
    while True:
        try:
            if not stop_flag.get('running', True):
                logger.info("[状态更新线程] 收到停止信号，线程退出。")
                break
        except (EOFError, BrokenPipeError):
            logger.info("[状态更新线程] 共享状态连接已关闭，线程退出。")
            break
        # --- 修改结束 ---

        try:
            update = status_update_queue.get(timeout=0.1)  # 使用新的变量名
            update_type = update.get('type')
            
            try:
                if update_type == "object_map_updated":
                    # 处理物体映射更新事件
                    new_map = update.get('object_map', {})
                    detection_objects = update.get('detection_objects', [])

                    # 1. 更新物体映射缓存
                    map_changed = update_object_map_from_detection(new_map)

                    # 2. 如果映射发生变化，更新队列并通知UI
                    if map_changed:
                        queue_snapshot = []
                        player_info_snapshot = {}

                        # 检查是否提供了必要的参数
                        if queue_lock and in_que is not None:
                            with queue_lock:
                                # 同步队列中的物体名称
                                sync_in_que_with_object_map(in_que, has_lock=True)
                                # 重新计算优先级
                                update_queue_priorities_inplace(in_que, player_info, player_comments, player_games, config)
                                queue_snapshot = copy.deepcopy(in_que)

                            with player_info_lock:
                                player_info_snapshot = copy.deepcopy(player_info)

                            # 通知UI更新
                            status_update_queue.put({
                                'type': 'queue_update',
                                'in_que': queue_snapshot,
                                'player_info': player_info_snapshot
                            })
                        else:
                            logger.error("queue_lock 或 in_que 未提供，无法同步队列中的物体名称！")

                    # 3. 更新WebDisplay（如果启用）
                    if web_display and web_display.is_initialized:
                        web_display.update_detection_display(detection_objects)

                # --- 新增：处理检测服务连接事件 ---
                elif update_type == "detect_service_event":
                    event_name = update.get('event_name')
                    if event_name == 'detect_service_disconnected':
                        logger.error("[检测服务故障] 检测服务连接断开，当前玩家将进入pending_retry_player。")
                        if detect_service_connected_event:
                            detect_service_connected_event.clear() # 标记服务已断开
                        
                        with pending_retry_player_lock:
                            if current_player.get('player_id'):
                                pending_retry_player = {
                                    'entry_tuple': (
                                        [
                                            current_player.get('plat', ''),
                                            current_player.get('start_time', ''),
                                            current_player.get('name', ''),
                                            current_player.get('player_id', ''),
                                            current_player.get('target_id', ''),
                                            current_player.get('head_img', ''),
                                            current_player.get('target_object', '未知')
                                        ],
                                        1.0,
                                        10.0,
                                        current_player.get('order_id')
                                    ),
                                    'current_player': current_player.copy()
                                }
                                logger.warning(f"[重试机制] 玩家 {current_player.get('name','')}({current_player.get('player_id','')}) 因检测服务断开已进入pending_retry_player。")
                            current_player.clear()

                    elif event_name == 'detect_service_reconnected':
                        logger.info("[重试机制] 检测到检测服务已恢复，若有pending_retry_player将立即重试。")
                        if detect_service_connected_event:
                            detect_service_connected_event.set() # 标记服务已恢复
                # --- 新增结束 ---

                elif update_type == "queue_update":
                    queue_for_display = copy.deepcopy(update.get('in_que', []))
                    with player_info_lock:
                        player_info_for_display = copy.deepcopy(update.get('player_info', {}))

                    if virtual_player_manager:
                        for item in queue_for_display:
                            player_id = item[0][3]
                            if "VirtualPlayer" in player_id:
                                vp = virtual_player_manager.active_pool.get(player_id)
                                if vp:
                                    time_since_queue_min = (time.time() - vp.simulated_come_time) / 60
                                    current_comments = vp.base_comments + (time_since_queue_min * vp.comment_growth_rate_per_min)
                                    if player_id in player_info_for_display:
                                        player_info_for_display[player_id]['comments_after_game'] = int(current_comments)

                    if web_display and web_display.is_initialized:
                        queue_data = prepare_queue_data_for_web(
                            queue_for_display, 
                            player_info_for_display, 
                            config
                        )
                        if queue_data:
                            web_display.update_queue(queue_data)
                    
                    # 发送到所有GUI队列
                    if gui_update_queues:
                        player_games_for_display = {}
                        with player_games_lock:
                            player_games_for_display = copy.deepcopy(player_games)

                        data_for_gui_queue = {
                            'type': 'queue_update',
                            'queue_size': len(queue_for_display),
                            'queue_data': queue_for_display,
                            'player_info_data': player_info_for_display,
                            'player_games_data': player_games_for_display
                        }
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(data_for_gui_queue)
                                except Exception as e:
                                    logger.warning(f"发送队列更新到GUI队列时出错: {e}")
                
                elif update_type == "new_comment":
                    if gui_update_queues:
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(update)
                                except Exception as e:
                                    logger.warning(f"发送新评论到GUI队列时出错: {e}")
                
                elif update_type == "object_picked":
                    player_id = update.get('player_id')
                    player_name = update.get('player_name', '未知玩家')
                    source_mode = update.get('source', 'unknown')
                    request_id = update.get('request_id')
                    success = update.get('success', False)
                    object_id = update.get('object_id')
                    message = update.get('message', '')
                    item_name = update.get('item_name', 'nothing')

                    logger.info(f"统一事件 'object_picked': 玩家 {player_name}({player_id}), 成功: {success}, 物品: {item_name}, 物品ID: {object_id}, 来源: {source_mode}, ReqID: {request_id}, 消息: {message}")

                    is_successful_pick = success
                    # --- 提前计算成功次数，并立即释放锁 ---
                    success_count = 0
                    if is_successful_pick:
                        with player_games_lock:
                            # 计算历史成功次数，然后加上本次的1次
                            history_successes = sum(1 for game in player_games.get(player_id, []) if game[1].lower() != 'nothing')
                            success_count = history_successes + 1
                        logger.info(f"玩家 {player_name} 本次是第 {success_count} 次成功抓取。")
                    # --- 锁已在此处释放 ---
                    
                    # --- 根据配置获取优惠券提示文本 ---
                    coupon_text = None
                    if is_successful_pick:
                        coupon_text = get_coupon_template_by_success_count(config, success_count)
                        if coupon_text:
                            coupon_text = coupon_text.replace('{player}', player_name).replace('{item}', item_name)
                            logger.info(f"为玩家 {player_name} 生成优惠券提示: {coupon_text}")
                    # --- 结束 ---
                    
                    if gui_update_queues:
                        object_picked_data = {
                            'type': 'object_picked',
                            'player_name': player_name,
                            'item_name': item_name,
                            'success_count': success_count, # 将成功次数传递给GUI
                            'coupon_text': coupon_text  # 将优惠券文本传递给GUI
                        }
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(object_picked_data)
                                except Exception as e:
                                    logger.warning(f"发送物品抓取事件到GUI队列时出错: {e}")

                    # --- 新增：web_display和obs_controller延迟祝贺显示 ---
                    caught_display_delay = config.get('web_display', {}).get('display_settings', {}).get('caught_display_delay', 0)
                    if web_display and web_display.is_initialized:
                        if is_successful_pick:
                            if caught_display_delay and caught_display_delay > 0:
                                threading.Thread(
                                    target=lambda: (
                                        time.sleep(caught_display_delay),
                                        web_display.show_success_message(player_name, item_name, success_count)
                                    ),
                                    daemon=True
                                ).start()
                            else:
                                web_display.show_success_message(player_name, item_name, success_count)
                        else:
                            web_display.show_failure_message(player_name)
                    
                    if obs_controller:
                        if is_successful_pick:
                            if caught_display_delay and caught_display_delay > 0:
                                threading.Thread(
                                    target=lambda: (
                                        time.sleep(caught_display_delay),
                                        obs_controller.show_congrats_by_success_count(player_name, item_name, success_count)
                                    ),
                                    daemon=True
                                ).start()
                            else:
                                obs_controller.show_congrats_by_success_count(player_name, item_name, success_count)
                        else:
                            obs_controller.show_failure_effect()
                    
                    if current_player.get('player_id') == player_id and current_player.get('move_command_id') == request_id:
                        current_player['game_result'] = item_name
                    else:
                        logger.warning(f"收到 'object_picked' 事件，但与当前玩家/命令不匹配。 Event player: {player_name}({player_id}), request_id: {request_id}, CurrentPlayer: {current_player}")
                elif update_type == "game_end":
                    if gui_update_queues:
                        game_end_data = {'type': 'game_end'}
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(game_end_data)
                                except Exception as e:
                                    logger.warning(f"发送游戏结束事件到GUI队列时出错: {e}")
                
                elif update_type == "game_start":
                    player_name = update.get('name', '')
                    
                    if web_display and web_display.is_initialized:
                        web_display.clear_text()
                        web_display.show_playing_status(player_name)
                    
                    if gui_update_queues:
                        game_start_data = {
                            'type': 'game_start', 'player_id': update.get('player_id', ''),
                            'name': player_name,
                            'target_id': update.get('target_id', ''),
                            'target_object': update.get('target_object', ''),
                            'history_for_player': update.get('history_for_player', [])
                        }
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(game_start_data)
                                except Exception as e:
                                    logger.warning(f"发送游戏开始事件到GUI队列时出错: {e}")
                
                elif update_type == 'move_service_event':
                    event_name = update.get('event_name')
                    event_data = update.get('data', {})
                    player_name_from_event = update.get('player_name', '未知玩家')
                    player_id_from_event = update.get('player_id')
                    request_id_from_event = update.get('request_id')

                    if event_name == 'cycle_completed':
                        logger.info(f"移动服务事件 'cycle_completed' for player {player_name_from_event}({player_id_from_event})")
                        if current_player.get('player_id') == player_id_from_event and current_player.get('move_command_id') == request_id_from_event:
                            final_item_name = current_player.get('game_result', 'nothing')
                            logger.info(f"从 current_player 获取到游戏结果: '{final_item_name}'，准备结束游戏。")
                            _handle_game_completion(
                                player_id=player_id_from_event, name=player_name_from_event,
                                captured_item=final_item_name, order_id=current_player.get('order_id'),
                                config=config, current_player=current_player, player_info=player_info,
                                player_games=player_games, db_sync_manager=db_sync_manager,
                                status_update_queue=status_update_queue,  # 修复：使用status_update_queue而不是status_update_event
                                pi_lock=player_info_lock,
                                pg_lock=player_games_lock, current_game_start_time=current_player.get('start_time', time.time()),
                                virtual_player_manager=virtual_player_manager
                            )
                        else:
                            logger.warning(f"收到 'cycle_completed' 事件，但与当前玩家/命令不匹配。 Event: {update}, CurrentPlayer: {current_player}")
                    
                    elif event_name == 'error':
                        error_message = event_data.get('message', '未知运动模块错误')
                        logger.error(f"移动服务报告错误: {error_message} (玩家: {player_name_from_event}, RequestID: {request_id_from_event}")
                        if current_player.get('player_id') == player_id_from_event and current_player.get('move_command_id') == request_id_from_event and current_player.get('status') == 'waiting_move_service':
                            logger.info(f"由于运动模块错误，将为玩家 {player_name_from_event} 结束游戏，结果视为 'nothing'。")
                            _handle_game_completion(
                                player_id=player_id_from_event, name=player_name_from_event,
                                captured_item='nothing', order_id=current_player.get('order_id'),
                                config=config, current_player=current_player, player_info=player_info,
                                player_games=player_games, db_sync_manager=db_sync_manager,
                                status_update_queue=status_update_queue,  # 修复：使用status_update_queue而不是status_update_event
                                pi_lock=player_info_lock,
                                pg_lock=player_games_lock, current_game_start_time=current_player.get('start_time', time.time()),
                                virtual_player_manager=virtual_player_manager
                            )

                # --- 处理硬件故障和运动模块故障事件 ---
                elif update_type == 'move_service_event':
                    event_name = update.get('event_name')
                    event_data = update.get('data', {})
                    player_name_from_event = update.get('player_name', '未知玩家')
                    player_id_from_event = update.get('player_id')
                    request_id_from_event = update.get('request_id')

                    if event_name == 'hardware_error' or event_name == 'operation_error':
                        # 记录详细故障日志
                        request_id = update.get('request_id')
                        player_name = update.get('player_name', '未知玩家')
                        player_id = update.get('player_id')
                        data = update.get('data', {})
                        logger.error(f"[移动服务端故障] {event_name} for {player_name}({player_id}), request_id={request_id}, data={json.dumps(data, ensure_ascii=False)}")
                        if event_name == 'hardware_error':
                            faults = data.get('faults', [])
                            for fault in faults:
                                logger.error(f"[硬件故障详情] axis={fault.get('axis')}, fault_type={fault.get('fault_type')}, fault_msg={fault.get('fault_msg')}")
                        # 保存pending_retry_player
                        # global pending_retry_player #不能再次声明
                        with pending_retry_player_lock:
                            if current_player.get('player_id'):
                                pending_retry_player = {
                                    'entry_tuple': (
                                        [
                                            current_player.get('plat', ''),
                                            current_player.get('start_time', ''),
                                            current_player.get('name', ''),
                                            current_player.get('player_id', ''),
                                            current_player.get('target_id', ''),
                                            current_player.get('head_img', ''),
                                            current_player.get('target_object', '未知')
                                        ],
                                        1.0,
                                        10.0,
                                        current_player.get('order_id')
                                    ),
                                    'current_player': current_player.copy()
                                }
                                logger.warning(f"[重试机制] 玩家 {current_player.get('name','')}({current_player.get('player_id','')}) 已进入pending_retry_player，等待移动服务端恢复后重试。")
                            current_player.clear()
                    elif event_name == 'move_service_disconnected':
                        logger.error("[移动服务端故障] 移动服务端连接断开，当前玩家将进入pending_retry_player。")
                        # global pending_retry_player #不能再次声明
                        with pending_retry_player_lock:
                            if current_player.get('player_id'):
                                pending_retry_player = {
                                    'entry_tuple': (
                                        [
                                            current_player.get('plat', ''),
                                            current_player.get('start_time', ''),
                                            current_player.get('name', ''),
                                            current_player.get('player_id', ''),
                                            current_player.get('target_id', ''),
                                            current_player.get('head_img', ''),
                                            current_player.get('target_object', '未知')
                                        ],
                                        1.0,
                                        10.0,
                                        current_player.get('order_id')
                                    ),
                                    'current_player': current_player.copy()
                                }
                                logger.warning(f"[重试机制] 玩家 {current_player.get('name','')}({current_player.get('player_id','')}) 已进入pending_retry_player，等待移动服务端恢复后重试。")
                            current_player.clear()
                    elif event_name == 'move_service_reconnected':
                        logger.info("[重试机制] 检测到移动服务端已恢复，若有pending_retry_player将立即重试。")
                        # 不需要额外处理，process_game_queue会自动优先处理pending_retry_player
                    elif event_name == 'move_service_shutdown':
                        # 服务器主动退出事件
                        reason = event_data.get('reason', 'unknown')
                        message = event_data.get('message', '服务器主动关闭')
                        logger.warning(f"[移动服务端] 服务器主动退出: {message} (原因: {reason})")
                        
                        # 与disconnect事件相同的处理逻辑
                        with pending_retry_player_lock:
                            if current_player.get('player_id'):
                                pending_retry_player = {
                                    'entry_tuple': (
                                        [
                                            current_player.get('plat', ''),
                                            current_player.get('start_time', ''),
                                            current_player.get('name', ''),
                                            current_player.get('player_id', ''),
                                            current_player.get('target_id', ''),
                                            current_player.get('head_img', ''),
                                            current_player.get('target_object', '未知')
                                        ],
                                        1.0,
                                        10.0,
                                        current_player.get('order_id')
                                    ),
                                    'current_player': current_player.copy()
                                }
                                logger.warning(f"[重试机制] 服务器主动关闭，玩家 {current_player.get('name','')}({current_player.get('player_id','')}) 已进入pending_retry_player，等待移动服务端重启后重试。")
                            current_player.clear()
                        
                    else:
                        logger.debug(f"收到其他移动服务事件: {update}")
                        
                elif update_type == "waiting_order":
                    player_name = update.get('player_name', '')
                    if web_display and web_display.is_initialized:
                        web_display.show_status_message('player_waiting_order', player_name)
                    if gui_update_queues:
                        waiting_order_data = {
                            'type': 'waiting_order',
                            'player_name': player_name
                        }
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(waiting_order_data)
                                except Exception as e:
                                    logger.warning(f"发送等待订单事件到GUI队列时出错: {e}")

                elif update_type == 'config_reload':
                    updated_sections = update.get('updated_sections', {})
                    if not updated_sections:
                        continue
                    logger.info(f"[配置重载] 收到配置更新事件，更新部分: {list(updated_sections.keys())}")
                    if update_global_config_func:
                        update_global_config_func(updated_sections)
                    else:
                        logger.error("[配置重载] 未提供全局配置更新函数，后端逻辑配置可能不会更新！")
                    if 'web_display' in updated_sections and web_display and web_display.is_initialized:
                        web_display.update_config(config.get('web_display', {}))
                        logger.info("已通知 WebDisplay 更新配置。")
                    if 'obs' in updated_sections and obs_controller:
                        obs_controller.update_config(config.get('obs', {}))

                        logger.info("已通知 OBS Controller 更新配置。")
                
                elif update_type == "object_selection_changed":
                    # 新增：处理编号变更事件，推送到GUI
                    requested_id = update.get('requested_object_id')
                    actual_id = update.get('actual_object_id')
                    actual_name = get_object_name_by_id(str(actual_id))
                    if gui_update_queues:
                        object_selection_changed_data = {
                            'type': 'object_selection_changed',
                            'requested_id': requested_id,
                            'actual_id': actual_id,
                            'actual_name': actual_name
                        }
                        for gui_queue in gui_update_queues:
                            if gui_queue:
                                try:
                                    gui_queue.put(object_selection_changed_data)
                                except Exception as e:
                                    logger.warning(f"发送物品选择变更事件到GUI队列时出错: {e}")

            except Exception as e:
                logger.error(f"处理事件 {update_type} 时出错: {e}")

                logger.error(traceback.format_exc())

            # status_update_event.task_done()  #没有地方join也就不需要它，有了反而容易出错
            
        except queue.Empty:
            continue
        except Exception as e:
            logger.error(f"处理状态更新时出错: {e}")
            logger.error(traceback.format_exc())
            time.sleep(0.5)

def get_orderbook_status_summary() -> Dict[str, int]:
    """获取订单状态簿的统计信息"""
    summary = {'available': 0, 'claimed': 0, 'total': 0}
    with orderbook_status_lock:
        for status in orderbook_status.values():
            summary[status] = summary.get(status, 0) + 1
        summary['total'] = len(orderbook_status)
    return summary

async def _actual_order_scraping_logic(scraper, config, earliest_creation_time_str, logger=None):
    """实际执行订单抓取的异步逻辑"""
    try:
        if logger:
            logger.info(f"[订单抓取] 开始抓取订单，最早创建时间: {earliest_creation_time_str or '不限'}")

        orders = await scraper.scrape_orders(
            earliest_creation_time_str=earliest_creation_time_str
        )

        if logger:
            logger.info(f"[订单抓取] 成功抓取到 {len(orders)} 个订单")

        return orders
    except Exception as e:
        if logger:
            logger.error(f"[订单抓取] 抓取过程出错: {e}")
            logger.error(traceback.format_exc())
        return []

def get_coupon_template_by_success_count(config, success_count):
    """根据成功抓取次数获取对应的优惠券模板文本（自适应最高模板）"""
    try:
        caught_item_config = config.get('web_display', {}).get('display_settings', {}).get('caught_item', {})
        sub_lines_config = caught_item_config.get('sub_lines', {}).get('lines', [])
        
        if not sub_lines_config or len(sub_lines_config) < 1 or not sub_lines_config[0].get('enabled', False):
            return None
            
        sub_line_config = sub_lines_config[0]
        
        # 1. 尝试直接匹配 template{success_count}
        direct_match_key = f'template{success_count}'
        if direct_match_key in sub_line_config:
            return sub_line_config.get(direct_match_key)
            
        # 2. 如果直接匹配失败，则查找可用的最高级模板
        import re
        template_keys = [k for k in sub_line_config if re.match(r'^template\d+$', k)]
        if not template_keys:
            # 3. 如果没有任何 templateX，则回退到通用 template
            return sub_line_config.get('template')

        # 提取模板编号并找到最大值
        max_template_num = 0
        for key in template_keys:
            try:
                num = int(re.search(r'\d+', key).group())
                if num > max_template_num:
                    max_template_num = num
            except (AttributeError, ValueError):
                continue
        
        if max_template_num > 0:
            # 返回最高级的可用模板
            return sub_line_config.get(f'template{max_template_num}')
            
        # 最后的备用方案
        return sub_line_config.get('template')

    except Exception as e:
        logger.error(f"获取优惠券模板时出错: {e}")
        return None

