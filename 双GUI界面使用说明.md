# 双GUI界面使用说明

## 功能概述

程序现在支持同时显示两个一模一样的GUI界面，方便在两个显示屏上同时显示游戏信息。两个GUI窗口的内容、布局、功能完全相同，数据更新也是同步的。

## 配置方法

### 1. 启用双GUI功能

在配置文件（如 `config.yaml`）中设置：

```yaml
displayer:
  enabled: true
  num_windows: 2  # 设置GUI窗口数量，默认为2
```

### 2. 调整窗口大小

由于需要显示两个窗口，建议调整窗口大小以避免重叠：

```yaml
displayer:
  window:
    default_width_ratio: 0.4  # 调小宽度比例
    default_height_ratio: 0.6
```

### 3. 完整配置示例

参考 `dual_gui_config_example.yaml` 文件中的完整配置示例。

## 窗口布局

- **窗口1**：自动放置在屏幕左侧（坐标：50, 50）
- **窗口2**：自动放置在屏幕右侧（坐标：屏幕宽度/2 + 50, 50）

每个窗口的标题会显示对应的编号：
- "游戏助手实时信息面板 - 显示器1"
- "游戏助手实时信息面板 - 显示器2"

## 功能特性

### 1. 完全同步的数据更新
- 两个GUI窗口显示完全相同的内容
- 所有数据更新（队列、当前玩家、游戏状态等）都会同时发送到两个窗口
- 实时FPS信息也会同步显示

### 2. 独立的进程管理
- 每个GUI窗口运行在独立的进程中
- 如果一个窗口崩溃，不会影响另一个窗口
- 程序退出时会正确清理所有GUI进程

### 3. 相同的界面布局
- 左侧面板：排队列表和评论区
- 右侧面板：当前玩家信息、状态信息、直播提示词
- 所有字体、颜色、布局配置都完全相同

## 技术实现

### 1. 多进程架构
- 主进程负责游戏逻辑和数据处理
- 每个GUI窗口运行在独立的子进程中
- 通过多进程队列（multiprocessing.Queue）进行通信

### 2. 数据广播机制
- 主进程将所有GUI更新事件广播到所有GUI队列
- 每个GUI进程独立处理接收到的更新事件
- 确保所有窗口显示的内容完全一致

### 3. 错误处理
- 如果某个GUI队列发送失败，不会影响其他GUI窗口
- 程序会记录错误日志但继续运行
- 支持优雅的进程清理和资源释放

## 使用场景

1. **双显示器设置**：在两个显示器上同时显示游戏信息
2. **直播场景**：一个窗口用于主播查看，另一个用于OBS采集
3. **监控需求**：多个位置同时监控游戏状态
4. **备份显示**：提供冗余显示，防止单点故障

## 注意事项

1. **性能影响**：运行两个GUI窗口会增加CPU和内存使用
2. **窗口位置**：程序会自动调整窗口位置，避免重叠
3. **配置同步**：两个窗口使用相同的配置，无法单独设置
4. **关闭行为**：关闭任一GUI窗口都会触发整个程序的退出流程

## 故障排除

### 1. 窗口无法显示
- 检查 `displayer.enabled` 是否为 `true`
- 确认 `num_windows` 配置正确
- 查看日志中的GUI进程启动信息

### 2. 窗口重叠
- 调整 `default_width_ratio` 和 `default_height_ratio`
- 手动拖拽窗口到合适位置

### 3. 数据不同步
- 检查日志中是否有队列发送错误
- 确认所有GUI进程都正常运行

## 扩展性

如果需要更多GUI窗口，只需修改 `num_windows` 配置：

```yaml
displayer:
  num_windows: 3  # 显示3个窗口
```

程序会自动创建对应数量的GUI进程和通信队列。
