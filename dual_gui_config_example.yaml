# 双GUI窗口配置示例
# 这个文件展示了如何配置程序以显示两个一模一样的GUI界面

displayer:
  enabled: true
  num_windows: 2  # 新增配置：GUI窗口数量，默认为2
  
  window:
    title: "游戏助手实时信息面板"
    default_width_ratio: 0.4  # 调小窗口宽度比例，为两个窗口留出空间
    default_height_ratio: 0.6
    background_color: "#DDDDDD"
  
  layout:
    left_panel_ratio: 0.6
    queue_panel_height_ratio: 0.7
  
  fonts:
    # 全局玩家名颜色
    global_colors:
      player_name: '#eb24c1'  # 粉红色
    
    # 基础字体配置
    player_font:
      family: 'Segoe UI Emoji Regular'
      weight: 'normal'
    
    data_font:
      family: 'Microsoft YaHei UI'
      weight: 'normal'
    
    # 各区域字体配置
    current_player:
      size: 14
      color: '#FFFFFF'
    
    status_info:
      size: 12
      color: '#CCCCCC'
    
    queue_list:
      size: 12
      color: '#E0E0E0'
    
    activity_log:
      size: 11
      colors:
        comment_text: '#98FB98'
        default_text: '#E0E0E0'
    
    commentary_panel:
      size: 18
      color: '#FFD700'
  
  text_formatting:
    max_active_name_length: 10
    max_active_comment_length: 50
    max_history_rewards_length: 50
  
  active_players_max_lines: 30

# 其他配置保持不变...
game:
  max_free_games_per_session: 1

message_server:
  host: '127.0.0.1'
  port: 9999
  route: '/game-da302d82'

poll_interval: 1
heartbeat_interval: 1

# 检测服务配置
Detect_server:
  enabled: true
  host: '127.0.0.1'
  port: 5555

# 移动服务配置
motormove_service:
  host: 'localhost'
  port: 5556

# Web显示配置
web_display:
  enabled: false

# 订单系统配置
orders:
  enabled: false

# 虚拟玩家配置
virtual_player:
  enabled: false

# 直播词提示文件
commentary_file: '直播词提示.txt'
